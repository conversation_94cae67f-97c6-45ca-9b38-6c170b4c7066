name: Build & Deploy NX Cache Server

on:
  push:
    branches:
      - main
    paths:
      - '**'
      - '!.github/workflows/**'
  workflow_dispatch:

env:
  SERVICE_NAME: nx-cache-server

jobs:
  build-push-image:
    name: 🏗 Build and Push
    runs-on: otto-common-runners
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: 🐳 Build and Push Image
        uses: roofstock/ci-cd/actions/build-container-ecr@main
        with:
          image-tag: latest
          repository-name: ${{ env.SERVICE_NAME }} 

  deploy:
    name: Deploy to AWS ECS
    runs-on: otto-common-runners
    needs: build-push-image
    steps:
      - name: Deploy to ECS
        uses: roofstock/ci-cd/actions/ecs-task-deploy@main
        with:
          RS_ACTIONS_BUILDER_APP_ID: ${{ secrets.RS_ACTIONS_BUILDER_APP_ID }}
          RS_ACTIONS_BUILDER_PRIV_KEY: ${{ secrets.RS_ACTIONS_BUILDER_PRIV_KEY }}
          SLACK_APP_TOKEN: ${{ secrets.SLACK_APP_TOKEN }}
          cluster-name: 'ops-cluster-tf'
          service-name: ${{ env.SERVICE_NAME }}
          new-image: '${{ env.SERVICE_NAME }}:latest'
          service-team: 'infra'
          update-service: 'True'
          update-variables: 'True'
          environment: 'ops'
