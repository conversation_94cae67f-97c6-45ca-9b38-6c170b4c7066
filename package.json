{"name": "@roofstock/nx-cache-server", "version": "1.0.0", "description": "Nx cache server with AWS S3 backend", "main": "src/main.js", "type": "module", "scripts": {"start": "node src/main.js", "dev": "nodemon src/main.js", "build": "echo 'No build step required'", "test": "echo 'No tests yet'"}, "dependencies": {"@aws-sdk/client-s3": "^3.895.0", "dd-trace": "^5.67.0", "express": "^5.1.0", "pino": "^8.15.0", "pino-pretty": "^10.2.0", "prom-client": "^15.1.3"}, "devDependencies": {"nodemon": "^3.1.10"}, "engines": {"node": ">=22.0.0"}}