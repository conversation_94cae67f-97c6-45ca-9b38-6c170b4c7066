import { Histogram, Counter, Gauge } from 'prom-client';

export const httpRequestDuration = new Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status_code'],
  buckets: [0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10]
});

export const httpRequestsTotal = new Counter({
  name: 'http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status_code']
});

export const cacheOperationsTotal = new Counter({
  name: 'cache_operations_total',
  help: 'Total number of cache operations',
  labelNames: ['operation', 'status']
});

export const cacheArtifactSize = new Histogram({
  name: 'cache_artifact_size_bytes',
  help: 'Size of cache artifacts in bytes',
  labelNames: ['operation'],
  buckets: [1024, 10240, 102400, 1048576, 10485760, 104857600, 1073741824]
});

export const authenticationAttempts = new Counter({
  name: 'authentication_attempts_total',
  help: 'Total number of authentication attempts',
  labelNames: ['status']
});

export const s3Operations = new Counter({
  name: 's3_operations_total',
  help: 'Total number of S3 operations',
  labelNames: ['operation', 'status']
});

export const errorCounter = new Counter({
  name: 'errors_total',
  help: 'Total number of errors',
  labelNames: ['error_type', 'endpoint']
});

export const activeConnections = new Gauge({
  name: 'active_connections_total',
  help: 'Number of active connections'
});

export const concurrentOperations = new Gauge({
  name: 'concurrent_operations_total',
  help: 'Number of concurrent cache operations',
  labelNames: ['operation']
});

export const s3Latency = new Histogram({
  name: 's3_operation_duration_seconds',
  help: 'Duration of S3 operations in seconds',
  labelNames: ['operation'],
  buckets: [0.1, 0.5, 1, 2, 5, 10, 30]
});
